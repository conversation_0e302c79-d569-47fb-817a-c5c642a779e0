import { ImageStyleTemplate } from "../models/types";

/**
 * Generate an image prompt input based on content and style
 * @param content The content to generate an image for
 * @param style The style to use
 * @param styleDescription The description of the style
 * @param detailedDescription Detailed description of the style
 * @param aspectRatio Optional aspect ratio for the image (default: "1:1")
 * @param platform Optional platform for optimization (default: "social")
 * @returns The prompt input
 */
export const generateImagePromptInput = (
    content: string,
    style: string,
    styleDescription: string,
    detailedDescription: string,
    aspectRatio: string = "1:1",
    platform: string = "social"
) => {
    const platformSpecs = {
        social: "square format optimized for social media feeds",
        twitter: "horizontal format optimized for Twitter posts",
        instagram: "square or vertical format optimized for Instagram",
        linkedin: "horizontal format optimized for LinkedIn posts",
        story: "vertical 9:16 format optimized for stories",
    };

    const formatSpec =
        platformSpecs[platform as keyof typeof platformSpecs] ||
        platformSpecs.social;

    return `You are an expert AI image prompt engineer specializing in creating high-quality, engaging visual content for social media platforms.

TASK: Generate a detailed image prompt in ${styleDescription} style that will produce a visually striking image optimized for ${platform} platforms.

INPUT CONTENT:
<content>
${content}
</content>

STYLE REQUIREMENTS:
<style>
${style}
</style>

<style_description>
${detailedDescription}
</style_description>

TECHNICAL SPECIFICATIONS:
- Aspect ratio: ${aspectRatio}
- Format: ${formatSpec}
- Quality: High resolution, professional quality
- Optimization: Mobile-friendly viewing, high engagement potential

PROMPT STRUCTURE REQUIREMENTS:
Create your prompt following this exact structure:

1. MAIN SUBJECT: [Clear, specific description of the primary focal point]
2. COMPOSITION: [Layout and arrangement optimized for ${aspectRatio} format]
3. STYLE ELEMENTS: [Specific ${styleDescription} characteristics and techniques]
4. COLOR PALETTE: [Colors that enhance the style and content message]
5. LIGHTING: [Lighting setup that complements the style and mood]
6. MOOD & ATMOSPHERE: [Emotional tone and feeling]
7. TECHNICAL DETAILS: [Camera settings, rendering quality, specific style parameters]

QUALITY GUIDELINES:
- Use specific, actionable descriptors
- Include technical parameters when relevant
- Ensure visual clarity at small sizes
- Optimize for social media engagement
- Maintain style authenticity
- Keep total prompt under 200 words

OUTPUT FORMAT:
Provide only the image description prompt. Do not include instructions like "create an image of" or "generate". Write as if describing an existing masterpiece.

EXAMPLE STRUCTURE:
[Main subject description], [composition details], rendered in [style] with [specific style elements], [color palette], [lighting description], [mood], [technical quality parameters]

Generate the prompt now:`;
};

/**
 * Detailed style descriptions for image generation
 */
export const styleDescriptions = {
    // Original styles
    photorealistic: `Photorealism aims to create images indistinguishable from photographs with extreme detail and precision in all elements, accurate lighting and shadows, proper perspective, natural textures, depth of field effects, subtle imperfections for authenticity, color accuracy, and natural environmental elements. This style prioritizes technical accuracy over artistic interpretation.`,
    watercolor: `Watercolor style features soft, transparent colors with visible paper texture, fluid color transitions, gentle color bleeding, white space as a design element, soft edges, layered washes, and a delicate, ethereal quality. This style emphasizes lightness and spontaneity with a characteristic luminosity.`,
    pixel_art: `Pixel art features deliberately limited resolution with visible square pixels, restricted color palettes, sharp edges, no anti-aliasing, careful placement of individual pixels, and often uses dithering for gradients. This style embraces digital constraints for a distinctive retro aesthetic.`,
    oil_painting: `Oil painting style features rich, vibrant colors with visible brushstrokes, textured surfaces, depth through layering, subtle color blending, dramatic lighting effects, and a sense of permanence and substance. This style emphasizes the materiality of paint and the artist's hand.`,
    pencil_sketch: `Pencil sketch style features fine, precise lines with varying pressure, hatching and cross-hatching for shading, minimal or no color, emphasis on form and contour, attention to light and shadow, and a raw, immediate quality. This style emphasizes draftsmanship and observation.`,
    cyberpunk: `Cyberpunk style features neon-lit urban dystopias with high-tech elements contrasted against decay, holographic displays, cybernetic modifications, rain-slicked streets reflecting lights, stark color contrasts (especially purples, blues, and pinks), and a gritty, noir atmosphere. This style emphasizes the juxtaposition of advanced technology and social breakdown.`,
    impressionist: `Impressionist style features visible brushstrokes with broken color, emphasis on light and its changing qualities, outdoor settings, vibrant complementary colors, scenes from everyday life, and a sense of spontaneity and movement. This style prioritizes the impression of a moment over detail.`,
    abstract: `Abstract style features non-representational forms with emphasis on color, shape, and line, freedom from recognizable subject matter, emotional expression through formal elements, bold compositions, and often geometric or organic patterns. This style prioritizes visual language over literal representation.`,
    pop_art: `Pop art features bold, simple imagery with vibrant, saturated colors, flat areas of color with strong outlines, repetition and patterns, imagery from popular culture and advertising, and often includes irony or humor. This style embraces commercial techniques and mass culture.`,
    low_poly: `Low poly style features geometric, faceted surfaces with a limited number of polygons, flat or smoothly graded color on each face, simplified forms, clean edges, and a distinctive 3D rendered look. This style embraces digital minimalism through reduced complexity.`,
    isometric: `Isometric style features a specific 30-degree angular perspective with no vanishing point, creating a 3D effect where vertical lines remain vertical and horizontal lines are drawn at 30-degree angles. Characterized by clean geometric shapes, precise edges, consistent scale across the image, and often includes architectural or technical elements. This style emphasizes clarity and technical precision in a three-dimensional space.`,
    ukiyo_e: `Ukiyo-e style features traditional Japanese woodblock print aesthetics with flat color planes, bold outlines, distinctive compositions with asymmetrical balance, detailed patterns, natural themes, and dramatic perspectives. Characterized by rich colors, stylized clouds and waves, careful attention to textile patterns, and often incorporates calligraphic elements. This style emphasizes elegant simplicity with decorative detail.`,

    // Medium styles
    stencil: `Stencil style features bold, minimalist designs created by spraying or rolling paint over cutout shapes. Characterized by flat, overlapping colors, sharp edges, high contrast, and simplified forms. This style emphasizes graphic impact and works best for simple subject matters with strong silhouettes.`,
    papercraft: `Papercraft style features three-dimensional paper constructions with clean folds, layered elements, and geometric precision. Characterized by flat color planes, sharp edges, dimensional depth, and often includes cut-out effects and shadow play. This style emphasizes craftsmanship and structural design.`,
    marker_illustration: `Marker illustration style features vibrant colors applied with colored markers, creating sharp edges and unique shading effects through color blending. Characterized by saturated hues, visible stroke patterns, and the distinctive texture of marker application. This style is commonly seen in fashion design and comic art.`,
    risograph: `Risograph style features unique textures and vibrant colors similar to screen printing, with slightly unpredictable ink coverage creating variations in color density. Characterized by grain texture, limited color palettes, and a distinctive retro aesthetic often used for posters and art prints.`,
    graffiti: `Graffiti style features bold, expressive lettering and imagery with vibrant colors, dynamic compositions, and urban aesthetics. Characterized by spray paint textures, layered elements, strong outlines, and often includes stylized characters and backgrounds.`,
    ink_wash: `Ink wash style features fluid, translucent black ink applied in varying concentrations to create gradations from light to dark. Characterized by organic flow, subtle gradients, minimal color, and emphasis on form and movement through tonal variation.`,
    quilling: `Quilling style features intricate designs created from rolled, shaped, and glued paper strips. Characterized by delicate spiral patterns, dimensional texture, precise geometric forms, and often creates mandala-like or floral compositions with fine detail work.`,
    charcoal: `Charcoal style features rich, deep blacks with soft gradations and smudged textures. Characterized by dramatic contrast, expressive mark-making, atmospheric effects, and the ability to create both fine details and broad tonal areas.`,
    collage: `Collage style features compositions created from various materials, papers, and images assembled together. Characterized by mixed textures, layered elements, contrasting scales, and often includes found imagery combined in unexpected ways.`,
    mosaic: `Mosaic style features images composed of small, distinct pieces of colored material arranged to form patterns or pictures. Characterized by tessellated surfaces, geometric precision, rich color combinations, and the interplay between individual elements and the overall composition.`,

    // Material styles
    porcelain: `Porcelain style features glossy, smooth surfaces with a characteristic white or light-colored ceramic appearance. Characterized by refined elegance, delicate translucency, often with decorative patterns, and a pristine, polished finish that reflects light beautifully.`,
    light: `Light style features subjects rendered as glowing, ethereal objects with luminous properties. Characterized by radiant effects, soft illumination, transparency, and often creates magical or otherworldly atmospheres with bright, luminous qualities.`,
    candy: `Candy style features bright, saturated colors with glossy, sweet-looking surfaces. Characterized by vibrant hues, smooth textures, playful aesthetics, and often includes translucent or crystalline effects that evoke confectionery treats.`,
    bubbles: `Bubble style features translucent, spherical forms with iridescent surfaces and light refraction effects. Characterized by transparency, rainbow reflections, delicate fragility, and floating, weightless qualities.`,
    crystals: `Crystal style features faceted, geometric surfaces with light refraction and prismatic effects. Characterized by angular cuts, brilliant reflections, transparency or translucency, and often displays rainbow spectrum effects through the crystal structure.`,
    ceramic: `Ceramic style features smooth, fired clay surfaces with matte or glazed finishes. Characterized by earthy textures, handcrafted qualities, often with visible throwing marks or glazing effects, and warm, organic aesthetics.`,
    plastic: `Plastic style features smooth, synthetic surfaces with uniform coloring and modern industrial aesthetics. Characterized by clean lines, bright colors, reflective or matte finishes, and contemporary manufactured appearance.`,
    wood: `Wood style features natural grain patterns, organic textures, and warm earth tones. Characterized by visible wood grain, natural imperfections, rich brown hues, and tactile, organic surfaces that convey craftsmanship and natural beauty.`,
    metal: `Metal style features reflective, industrial surfaces with metallic luster and cool tones. Characterized by high reflectivity, sharp edges, industrial precision, and often includes oxidation, patina, or polished finishes.`,
    water: `Water style features fluid, transparent surfaces with flowing movement and light refraction. Characterized by transparency, ripple effects, reflective properties, and dynamic movement that captures the essence of liquid motion.`,
    glass: `Glass style features transparent or translucent surfaces with light refraction and reflection. Characterized by crystal clarity, smooth surfaces, light play, and often includes subtle distortions or prismatic effects.`,
    sand: `Sand style features granular textures with warm, earthy tones and natural, weathered appearance. Characterized by fine particle textures, desert colors, natural erosion patterns, and organic, flowing forms.`,
    rain: `Rain style features water droplets, wet surfaces, and atmospheric moisture effects. Characterized by reflective wet surfaces, droplet patterns, misty atmospheres, and the fresh, clean aesthetic of precipitation.`,

    // Photography styles
    high_key_photograph: `High key photography features bright, well-illuminated subjects with minimal shadows and an optimistic feel. Characterized by overexposed highlights, soft lighting, light tones, and generally cheerful, airy atmospheres.`,
    low_key_photograph: `Low key photography features dramatic shadows and dark tones with moody, mysterious atmospheres. Characterized by underexposed areas, strong contrast, minimal lighting, and often conveys tension or drama.`,
    low_angle_photograph: `Low angle photography captures subjects from below, making them appear imposing and powerful. Characterized by upward perspective, dramatic sky backgrounds, enhanced subject prominence, and often conveys strength or authority.`,
    high_angle_photograph: `High angle photography captures subjects from above, making them appear vulnerable or isolated. Characterized by downward perspective, environmental context, diminished subject scale, and often conveys vulnerability or overview.`,
    extreme_close_up: `Extreme close-up photography fills the frame with small details, often cutting off parts of the subject. Characterized by intimate detail focus, shallow depth of field, texture emphasis, and reveals hidden or overlooked elements.`,
    low_shutter_speed_photograph: `Low shutter speed photography captures motion blur and light trails with the camera's shutter open longer. Characterized by motion streaks, light trails, dynamic movement, and often shows the passage of time.`,
    bokeh_photograph: `Bokeh photography features sharp subjects with beautifully blurred backgrounds. Characterized by shallow depth of field, creamy background blur, subject isolation, and often includes circular light highlights.`,
    silhouette_photograph: `Silhouette photography features dark subject outlines against bright backgrounds. Characterized by strong contrast, shape emphasis, dramatic lighting, and often creates mysterious or artistic effects.`,
    black_and_white_photograph: `Black and white photography removes color to emphasize form, texture, and contrast. Characterized by monochromatic tones, dramatic shadows, texture emphasis, and timeless, classic aesthetics.`,
    birds_eye_view: `Bird's-eye view photography captures subjects from directly above. Characterized by top-down perspective, pattern revelation, spatial relationships, and often creates abstract or geometric compositions.`,
    worms_eye_view: `Worm's-eye view photography captures subjects from ground level looking up. Characterized by extreme low angle, dramatic perspective, sky prominence, and often creates powerful, imposing compositions.`,
    dutch_angle: `Dutch angle photography tilts the camera to create diagonal compositions. Characterized by tilted horizon lines, dynamic tension, unbalanced feeling, and often conveys unease or excitement.`,
    long_exposure_photograph: `Long exposure photography uses extended shutter times to capture movement over time. Characterized by smooth water, light trails, cloud streaks, and ethereal, dreamlike motion effects.`,

    // Lighting styles
    natural_lighting: `Natural lighting uses sunlight or moonlight to illuminate subjects authentically. Characterized by realistic shadows, warm or cool color temperatures, directional light, and creates authentic, believable atmospheres.`,
    light_and_shadow: `Light and shadow style emphasizes dramatic contrasts between illuminated and dark areas. Characterized by strong directional lighting, deep shadows, high contrast, and often creates moody, dramatic compositions.`,
    volumetric_lighting: `Volumetric lighting features visible light beams illuminated by dust, smoke, or fog. Characterized by three-dimensional light rays, atmospheric effects, dramatic illumination, and often creates mystical or cinematic moods.`,
    neon_lighting: `Neon lighting features bright, saturated artificial light with retro-futuristic aesthetics. Characterized by vibrant colors, electric glow, urban nighttime settings, and often conveys modern, edgy, or cyberpunk themes.`,
    golden_hour: `Golden hour lighting captures the warm, soft light just after sunrise or before sunset. Characterized by golden color temperature, long shadows, romantic atmosphere, and flattering, diffused illumination.`,
    blue_hour: `Blue hour lighting captures the deep blue twilight just after sunset or before sunrise. Characterized by cool blue tones, balanced artificial and natural light, serene atmosphere, and often includes city lights.`,
    backlighting: `Backlighting places the light source behind the subject, creating silhouettes or rim lighting. Characterized by subject outlining, lens flare effects, dramatic contrast, and often creates ethereal or mysterious moods.`,
    chiaroscuro: `Chiaroscuro lighting features dramatic contrasts between light and dark areas. Characterized by strong directional light, deep shadows, Renaissance-inspired aesthetics, and creates powerful, artistic compositions.`,
    god_rays: `God rays lighting features dramatic beams of light breaking through clouds or openings. Characterized by visible light shafts, spiritual atmosphere, natural drama, and often conveys divine or transcendent themes.`,
    candlelight: `Candlelight features warm, flickering illumination with intimate, cozy atmospheres. Characterized by warm orange tones, soft shadows, romantic mood, and creates intimate, traditional settings.`,
    street_lighting: `Street lighting features urban artificial illumination with nighttime city aesthetics. Characterized by orange sodium lights, urban atmosphere, night photography, and often conveys metropolitan or noir themes.`,
    softbox_lighting: `Softbox lighting features diffused, even illumination commonly used in studio photography. Characterized by soft shadows, even light distribution, professional quality, and creates clean, commercial aesthetics.`,
    moonlight: `Moonlight features cool, silvery illumination with mysterious nighttime atmospheres. Characterized by blue-white tones, soft shadows, romantic or mysterious mood, and creates ethereal, nocturnal settings.`,
    fairy_lights: `Fairy lights feature small, twinkling illumination with magical, whimsical atmospheres. Characterized by warm point lights, bokeh effects, festive mood, and creates enchanting, celebratory settings.`,

    // Color and palette styles
    cool_tones: `Cool tones feature blues, greens, and purples that evoke calm and tranquility. Characterized by soothing color palettes, peaceful atmospheres, refreshing aesthetics, and often conveys serenity or professionalism.`,
    warm_tones: `Warm tones feature reds, oranges, and yellows that create inviting and comfortable feelings. Characterized by cozy color palettes, energetic atmospheres, welcoming aesthetics, and often conveys comfort or excitement.`,
    pastels: `Pastel colors feature soft, muted tones with high lightness and low saturation. Characterized by gentle color palettes, dreamy atmospheres, delicate aesthetics, and often conveys innocence or romance.`,
    vibrant: `Vibrant colors feature highly saturated, intense hues with maximum color impact. Characterized by bold color palettes, energetic atmospheres, attention-grabbing aesthetics, and often conveys excitement or modernity.`,
    earth_tones: `Earth tones feature colors inspired by nature including browns, tans, and muted greens. Characterized by natural color palettes, grounded atmospheres, organic aesthetics, and often conveys stability or authenticity.`,
    jewel_tones: `Jewel tones feature deeply saturated colors inspired by precious stones. Characterized by rich color palettes, luxurious atmospheres, sophisticated aesthetics, and often conveys elegance or opulence.`,
    monochromatic_blues: `Monochromatic blues feature various shades and tints of blue only. Characterized by unified color schemes, calming atmospheres, cohesive aesthetics, and often conveys depth or tranquility.`,
    earthy_reds_and_oranges: `Earthy reds and oranges feature warm, natural tones inspired by autumn and earth. Characterized by rustic color palettes, cozy atmospheres, natural aesthetics, and often conveys warmth or harvest themes.`,
    neon_graffiti: `Neon graffiti colors feature bright, electric hues with urban street art aesthetics. Characterized by fluorescent color palettes, edgy atmospheres, rebellious aesthetics, and often conveys youth culture or urban energy.`,
    autumn_leaves: `Autumn leaves colors feature the warm palette of fall foliage. Characterized by seasonal color schemes, nostalgic atmospheres, natural beauty, and often conveys change or the passage of time.`,
    deep_sea_blues: `Deep sea blues feature dark, mysterious blue tones inspired by ocean depths. Characterized by profound color palettes, mysterious atmospheres, aquatic aesthetics, and often conveys depth or mystery.`,
    grayscale: `Grayscale features only black, white, and gray tones without color. Characterized by monochromatic schemes, timeless atmospheres, classic aesthetics, and often conveys sophistication or drama.`,
    sepia: `Sepia features warm brown monochromatic tones reminiscent of vintage photography. Characterized by nostalgic color schemes, aged atmospheres, historical aesthetics, and often conveys memory or antiquity.`,
    primary_colors: `Primary colors feature pure red, blue, and yellow in bold combinations. Characterized by fundamental color schemes, bold atmospheres, graphic aesthetics, and often conveys simplicity or childhood themes.`,
    rainbow_spectrum: `Rainbow spectrum features the full range of visible colors in vibrant display. Characterized by complete color palettes, joyful atmospheres, celebratory aesthetics, and often conveys diversity or pride.`,
    metallics: `Metallic colors feature reflective, lustrous tones including gold, silver, and copper. Characterized by luxurious color palettes, sophisticated atmospheres, premium aesthetics, and often conveys wealth or modernity.`,
};

/**
 * Templates for each image style
 */
export const imageStyleTemplates: Record<string, ImageStyleTemplate> = {
    photorealistic: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in photorealistic image generation. Create detailed, technically precise prompts that produce lifelike images optimized for social media engagement. Focus on camera settings, lighting conditions, and realistic details. Include specific technical parameters like aperture, ISO, and lens specifications when relevant. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "photorealistic",
                "photorealistic",
                styleDescriptions.photorealistic,
                aspectRatio,
                platform
            ),
    },

    watercolor: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in watercolor-style image generation. Create prompts that capture the fluid, transparent nature of watercolor painting with visible paper texture and organic color bleeding. Focus on soft edges, luminous transparency, and the characteristic spontaneity of watercolor techniques. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "watercolor",
                "watercolor",
                styleDescriptions.watercolor,
                aspectRatio,
                platform
            ),
    },

    oil_painting: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in oil painting-style image generation. Create prompts that emphasize rich textures, visible brushstrokes, and the substantial quality of oil paint. Focus on color depth, impasto techniques, and the traditional craftsmanship of oil painting. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "oil painting",
                "oil painting",
                styleDescriptions.oil_painting,
                aspectRatio,
                platform
            ),
    },

    pencil_sketch: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in pencil sketch-style image generation. Create prompts that emphasize line quality, hatching techniques, and the raw immediacy of pencil work. Focus on varying line weights, cross-hatching patterns, and the monochromatic beauty of graphite. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "pencil sketch",
                "pencil sketch",
                styleDescriptions.pencil_sketch,
                aspectRatio,
                platform
            ),
    },

    pixel_art: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in pixel art-style image generation. Create prompts that emphasize the deliberate constraints of pixel art: limited resolution, restricted color palettes, and sharp pixel-perfect edges. Focus on retro gaming aesthetics and digital minimalism. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "pixel art",
                "pixel art",
                styleDescriptions.pixel_art,
                aspectRatio,
                platform
            ),
    },

    cyberpunk: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in cyberpunk image generation. Create prompts that capture the high-tech, low-life aesthetic with neon lighting, urban decay, and futuristic elements. Focus on dramatic lighting contrasts, holographic displays, and the gritty atmosphere of dystopian futures. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "cyberpunk",
                "cyberpunk",
                styleDescriptions.cyberpunk,
                aspectRatio,
                platform
            ),
    },

    impressionist: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in impressionist image generation. Create prompts that capture the essence of impressionist painting with visible brushstrokes, broken color, and emphasis on light. Focus on outdoor scenes, color vibrance, and the spontaneous quality of plein air painting. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "impressionist",
                "impressionist",
                styleDescriptions.impressionist,
                aspectRatio,
                platform
            ),
    },

    abstract: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in abstract image generation. Create prompts that emphasize non-representational forms, color relationships, and emotional expression through visual elements. Focus on composition, color theory, and the pure language of visual art. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "abstract",
                "abstract",
                styleDescriptions.abstract,
                aspectRatio,
                platform
            ),
    },

    pop_art: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in pop art image generation. Create prompts that capture the bold, commercial aesthetic of pop art with flat colors, strong outlines, and mass culture imagery. Focus on graphic impact, repetition patterns, and the ironic appropriation of commercial techniques. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "pop art",
                "pop art",
                styleDescriptions.pop_art,
                aspectRatio,
                platform
            ),
    },
    isometric: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in isometric image generation. Create prompts that emphasize the distinctive 30-degree perspective with clean geometric shapes and technical precision. Focus on architectural elements, consistent scale, and the clarity of isometric projection. Your output should be a direct image description without instructional language.`,
        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "isometric",
                "isometric",
                styleDescriptions.isometric,
                aspectRatio,
                platform
            ),
    },
    ukiyo_e: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in ukiyo-e image generation. Create prompts that capture the traditional Japanese woodblock print aesthetic with flat color planes, bold outlines, and asymmetrical compositions. Focus on stylized natural elements, rich colors, and the elegant simplicity of Japanese art. Your output should be a direct image description without instructional language.`,
        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "ukiyo-e",
                "ukiyo-e",
                styleDescriptions.ukiyo_e,
                aspectRatio,
                platform
            ),
    },

    low_poly: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in low poly image generation. Create prompts that emphasize geometric faceted surfaces with minimal polygon counts and clean edges. Focus on simplified forms, flat color gradients, and the distinctive 3D rendered aesthetic of low poly art. Your output should be a direct image description without instructional language.`,

        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "low poly",
                "low poly",
                styleDescriptions.low_poly,
                aspectRatio,
                platform
            ),
    },

    // Medium styles
    stencil: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in stencil-style image generation. Create prompts that emphasize bold, minimalist designs with high contrast and simplified forms. Focus on strong silhouettes, flat overlapping colors, and graphic impact. Your output should be a direct image description without instructional language.`,
        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "stencil",
                "stencil",
                styleDescriptions.stencil,
                aspectRatio,
                platform
            ),
    },

    papercraft: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in papercraft-style image generation. Create prompts that emphasize three-dimensional paper constructions with clean folds and geometric precision. Focus on layered elements, dimensional depth, and the crafted aesthetic of paper art. Your output should be a direct image description without instructional language.`,
        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "papercraft",
                "papercraft",
                styleDescriptions.papercraft,
                aspectRatio,
                platform
            ),
    },

    marker_illustration: {
        systemPrompt: `You are an expert AI image prompt engineer specializing in marker illustration-style image generation. Create prompts that capture the vibrant, hand-drawn quality of marker art with bold colors and visible stroke patterns. Focus on saturated hues, color blending effects, and dynamic compositions. Your output should be a direct image description without instructional language.`,
        getPromptInput: (content, aspectRatio = "1:1", platform = "social") =>
            generateImagePromptInput(
                content,
                "marker illustration",
                "marker illustration",
                styleDescriptions.marker_illustration,
                aspectRatio,
                platform
            ),
    },

    // Material styles
    porcelain: {
        systemPrompt: `You are an expert in writing prompts for porcelain-style image generation. You excel at creating elegant, ceramic visual descriptions with glossy surfaces and refined aesthetics that work well in social media feeds. Focus on smooth textures, delicate details, and pristine finishes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "porcelain",
                "porcelain",
                styleDescriptions.porcelain
            ),
    },

    light: {
        systemPrompt: `You are an expert in writing prompts for light-style image generation. You excel at creating luminous, glowing visual descriptions with ethereal light effects that work well in social media feeds. Focus on radiant qualities, soft illumination, and magical atmospheres that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "light",
                "light",
                styleDescriptions.light
            ),
    },

    candy: {
        systemPrompt: `You are an expert in writing prompts for candy-style image generation. You excel at creating sweet, colorful visual descriptions with glossy, confectionery aesthetics that work well in social media feeds. Focus on bright colors, smooth textures, and playful compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "candy",
                "candy",
                styleDescriptions.candy
            ),
    },

    // Missing medium styles
    risograph: {
        systemPrompt: `You are an expert in writing prompts for risograph-style image generation. You excel at creating unique textured visual descriptions with vibrant colors and screen printing aesthetics that work well in social media feeds. Focus on grain texture, limited color palettes, and retro poster aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "risograph",
                "risograph",
                styleDescriptions.risograph
            ),
    },

    graffiti: {
        systemPrompt: `You are an expert in writing prompts for graffiti-style image generation. You excel at creating bold, expressive visual descriptions with urban street art aesthetics that work well in social media feeds. Focus on spray paint textures, vibrant colors, and dynamic compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "graffiti",
                "graffiti",
                styleDescriptions.graffiti
            ),
    },

    ink_wash: {
        systemPrompt: `You are an expert in writing prompts for ink wash-style image generation. You excel at creating fluid, translucent visual descriptions with traditional ink painting aesthetics that work well in social media feeds. Focus on organic flow, subtle gradients, and tonal variation that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "ink wash",
                "ink wash",
                styleDescriptions.ink_wash
            ),
    },

    quilling: {
        systemPrompt: `You are an expert in writing prompts for quilling-style image generation. You excel at creating intricate, paper-rolled visual descriptions with delicate spiral patterns that work well in social media feeds. Focus on dimensional texture, precise geometric forms, and fine detail work that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "quilling",
                "quilling",
                styleDescriptions.quilling
            ),
    },

    charcoal: {
        systemPrompt: `You are an expert in writing prompts for charcoal-style image generation. You excel at creating rich, expressive visual descriptions with dramatic contrast and smudged textures that work well in social media feeds. Focus on deep blacks, soft gradations, and atmospheric effects that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "charcoal",
                "charcoal",
                styleDescriptions.charcoal
            ),
    },

    collage: {
        systemPrompt: `You are an expert in writing prompts for collage-style image generation. You excel at creating mixed-media visual descriptions with layered elements and contrasting textures that work well in social media feeds. Focus on assembled materials, varied scales, and unexpected combinations that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "collage",
                "collage",
                styleDescriptions.collage
            ),
    },

    mosaic: {
        systemPrompt: `You are an expert in writing prompts for mosaic-style image generation. You excel at creating tessellated visual descriptions with small distinct pieces forming patterns that work well in social media feeds. Focus on geometric precision, rich color combinations, and the interplay between individual elements that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "mosaic",
                "mosaic",
                styleDescriptions.mosaic
            ),
    },

    // Missing material styles
    bubbles: {
        systemPrompt: `You are an expert in writing prompts for bubble-style image generation. You excel at creating translucent, spherical visual descriptions with iridescent surfaces that work well in social media feeds. Focus on transparency, rainbow reflections, and floating weightless qualities that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "bubbles",
                "bubbles",
                styleDescriptions.bubbles
            ),
    },

    crystals: {
        systemPrompt: `You are an expert in writing prompts for crystal-style image generation. You excel at creating faceted, geometric visual descriptions with light refraction and prismatic effects that work well in social media feeds. Focus on angular cuts, brilliant reflections, and rainbow spectrum effects that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "crystals",
                "crystals",
                styleDescriptions.crystals
            ),
    },

    ceramic: {
        systemPrompt: `You are an expert in writing prompts for ceramic-style image generation. You excel at creating smooth, fired clay visual descriptions with handcrafted qualities that work well in social media feeds. Focus on earthy textures, glazing effects, and warm organic aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "ceramic",
                "ceramic",
                styleDescriptions.ceramic
            ),
    },

    plastic: {
        systemPrompt: `You are an expert in writing prompts for plastic-style image generation. You excel at creating smooth, synthetic visual descriptions with modern industrial aesthetics that work well in social media feeds. Focus on clean lines, bright colors, and contemporary manufactured appearance that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "plastic",
                "plastic",
                styleDescriptions.plastic
            ),
    },

    wood: {
        systemPrompt: `You are an expert in writing prompts for wood-style image generation. You excel at creating natural grain visual descriptions with organic textures and warm earth tones that work well in social media feeds. Focus on visible wood grain, natural imperfections, and tactile surfaces that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "wood",
                "wood",
                styleDescriptions.wood
            ),
    },

    metal: {
        systemPrompt: `You are an expert in writing prompts for metal-style image generation. You excel at creating reflective, industrial visual descriptions with metallic luster that work well in social media feeds. Focus on high reflectivity, sharp edges, and industrial precision that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "metal",
                "metal",
                styleDescriptions.metal
            ),
    },

    water: {
        systemPrompt: `You are an expert in writing prompts for water-style image generation. You excel at creating fluid, transparent visual descriptions with flowing movement that work well in social media feeds. Focus on transparency, ripple effects, and dynamic liquid motion that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "water",
                "water",
                styleDescriptions.water
            ),
    },

    glass: {
        systemPrompt: `You are an expert in writing prompts for glass-style image generation. You excel at creating transparent visual descriptions with light refraction and reflection that work well in social media feeds. Focus on crystal clarity, smooth surfaces, and light play that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "glass",
                "glass",
                styleDescriptions.glass
            ),
    },

    sand: {
        systemPrompt: `You are an expert in writing prompts for sand-style image generation. You excel at creating granular textured visual descriptions with warm, earthy tones that work well in social media feeds. Focus on fine particle textures, desert colors, and organic flowing forms that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "sand",
                "sand",
                styleDescriptions.sand
            ),
    },

    rain: {
        systemPrompt: `You are an expert in writing prompts for rain-style image generation. You excel at creating atmospheric visual descriptions with water droplets and wet surfaces that work well in social media feeds. Focus on reflective wet surfaces, droplet patterns, and fresh clean aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "rain",
                "rain",
                styleDescriptions.rain
            ),
    },

    // Photography styles
    high_key_photograph: {
        systemPrompt: `You are an expert in writing prompts for high key photography image generation. You excel at creating bright, well-illuminated visual descriptions with minimal shadows that work well in social media feeds. Focus on overexposed highlights, soft lighting, and cheerful atmospheres that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "high key photograph",
                "high key photograph",
                styleDescriptions.high_key_photograph
            ),
    },

    low_key_photograph: {
        systemPrompt: `You are an expert in writing prompts for low key photography image generation. You excel at creating dramatic, shadow-filled visual descriptions with moody atmospheres that work well in social media feeds. Focus on underexposed areas, strong contrast, and mysterious lighting that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "low key photograph",
                "low key photograph",
                styleDescriptions.low_key_photograph
            ),
    },

    low_angle_photograph: {
        systemPrompt: `You are an expert in writing prompts for low angle photography image generation. You excel at creating powerful, imposing visual descriptions captured from below that work well in social media feeds. Focus on upward perspective, dramatic sky backgrounds, and enhanced subject prominence that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "low angle photograph",
                "low angle photograph",
                styleDescriptions.low_angle_photograph
            ),
    },

    high_angle_photograph: {
        systemPrompt: `You are an expert in writing prompts for high angle photography image generation. You excel at creating overview visual descriptions captured from above that work well in social media feeds. Focus on downward perspective, environmental context, and diminished subject scale that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "high angle photograph",
                "high angle photograph",
                styleDescriptions.high_angle_photograph
            ),
    },

    extreme_close_up: {
        systemPrompt: `You are an expert in writing prompts for extreme close-up photography image generation. You excel at creating intimate detail visual descriptions that fill the frame that work well in social media feeds. Focus on texture emphasis, shallow depth of field, and revealing hidden elements that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "extreme close-up",
                "extreme close-up",
                styleDescriptions.extreme_close_up
            ),
    },

    low_shutter_speed_photograph: {
        systemPrompt: `You are an expert in writing prompts for low shutter speed photography image generation. You excel at creating motion blur visual descriptions with light trails that work well in social media feeds. Focus on motion streaks, dynamic movement, and the passage of time that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "low shutter speed photograph",
                "low shutter speed photograph",
                styleDescriptions.low_shutter_speed_photograph
            ),
    },

    bokeh_photograph: {
        systemPrompt: `You are an expert in writing prompts for bokeh photography image generation. You excel at creating sharp subject visual descriptions with beautifully blurred backgrounds that work well in social media feeds. Focus on shallow depth of field, creamy background blur, and subject isolation that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "bokeh photograph",
                "bokeh photograph",
                styleDescriptions.bokeh_photograph
            ),
    },

    silhouette_photograph: {
        systemPrompt: `You are an expert in writing prompts for silhouette photography image generation. You excel at creating dramatic outline visual descriptions against bright backgrounds that work well in social media feeds. Focus on strong contrast, shape emphasis, and mysterious effects that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "silhouette photograph",
                "silhouette photograph",
                styleDescriptions.silhouette_photograph
            ),
    },

    black_and_white_photograph: {
        systemPrompt: `You are an expert in writing prompts for black and white photography image generation. You excel at creating monochromatic visual descriptions that emphasize form and texture that work well in social media feeds. Focus on dramatic shadows, texture emphasis, and timeless aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "black and white photograph",
                "black and white photograph",
                styleDescriptions.black_and_white_photograph
            ),
    },

    birds_eye_view: {
        systemPrompt: `You are an expert in writing prompts for bird's-eye view photography image generation. You excel at creating top-down visual descriptions that reveal patterns and spatial relationships that work well in social media feeds. Focus on overhead perspective, pattern revelation, and geometric compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "bird's-eye view",
                "bird's-eye view",
                styleDescriptions.birds_eye_view
            ),
    },

    worms_eye_view: {
        systemPrompt: `You are an expert in writing prompts for worm's-eye view photography image generation. You excel at creating ground-level upward visual descriptions that create powerful compositions that work well in social media feeds. Focus on extreme low angle, dramatic perspective, and imposing compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "worm's-eye view",
                "worm's-eye view",
                styleDescriptions.worms_eye_view
            ),
    },

    dutch_angle: {
        systemPrompt: `You are an expert in writing prompts for Dutch angle photography image generation. You excel at creating tilted, dynamic visual descriptions that create tension that work well in social media feeds. Focus on diagonal compositions, dynamic tension, and unbalanced feeling that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "Dutch angle",
                "Dutch angle",
                styleDescriptions.dutch_angle
            ),
    },

    long_exposure_photograph: {
        systemPrompt: `You are an expert in writing prompts for long exposure photography image generation. You excel at creating ethereal visual descriptions that capture movement over time that work well in social media feeds. Focus on smooth water, light trails, and dreamlike motion effects that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "long exposure photograph",
                "long exposure photograph",
                styleDescriptions.long_exposure_photograph
            ),
    },

    // Lighting styles
    natural_lighting: {
        systemPrompt: `You are an expert in writing prompts for natural lighting image generation. You excel at creating authentic visual descriptions using sunlight or moonlight that work well in social media feeds. Focus on realistic shadows, warm or cool color temperatures, and believable atmospheres that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "natural lighting",
                "natural lighting",
                styleDescriptions.natural_lighting
            ),
    },

    light_and_shadow: {
        systemPrompt: `You are an expert in writing prompts for light and shadow image generation. You excel at creating dramatic visual descriptions with strong contrasts between illuminated and dark areas that work well in social media feeds. Focus on directional lighting, deep shadows, and moody compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "light and shadow",
                "light and shadow",
                styleDescriptions.light_and_shadow
            ),
    },

    volumetric_lighting: {
        systemPrompt: `You are an expert in writing prompts for volumetric lighting image generation. You excel at creating mystical visual descriptions with visible light beams that work well in social media feeds. Focus on three-dimensional light rays, atmospheric effects, and cinematic moods that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "volumetric lighting",
                "volumetric lighting",
                styleDescriptions.volumetric_lighting
            ),
    },

    neon_lighting: {
        systemPrompt: `You are an expert in writing prompts for neon lighting image generation. You excel at creating vibrant, electric visual descriptions with retro-futuristic aesthetics that work well in social media feeds. Focus on bright saturated colors, electric glow, and urban nighttime settings that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "neon lighting",
                "neon lighting",
                styleDescriptions.neon_lighting
            ),
    },

    golden_hour: {
        systemPrompt: `You are an expert in writing prompts for golden hour image generation. You excel at creating warm, romantic visual descriptions with soft sunset/sunrise light that work well in social media feeds. Focus on golden color temperature, long shadows, and flattering illumination that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "golden hour",
                "golden hour",
                styleDescriptions.golden_hour
            ),
    },

    blue_hour: {
        systemPrompt: `You are an expert in writing prompts for blue hour image generation. You excel at creating serene twilight visual descriptions with deep blue tones that work well in social media feeds. Focus on cool blue tones, balanced lighting, and peaceful atmospheres that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "blue hour",
                "blue hour",
                styleDescriptions.blue_hour
            ),
    },

    backlighting: {
        systemPrompt: `You are an expert in writing prompts for backlighting image generation. You excel at creating ethereal visual descriptions with light sources behind subjects that work well in social media feeds. Focus on subject outlining, lens flare effects, and mysterious moods that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "backlighting",
                "backlighting",
                styleDescriptions.backlighting
            ),
    },

    chiaroscuro: {
        systemPrompt: `You are an expert in writing prompts for chiaroscuro lighting image generation. You excel at creating Renaissance-inspired visual descriptions with dramatic light and dark contrasts that work well in social media feeds. Focus on strong directional light, deep shadows, and artistic compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "chiaroscuro",
                "chiaroscuro",
                styleDescriptions.chiaroscuro
            ),
    },

    god_rays: {
        systemPrompt: `You are an expert in writing prompts for god rays lighting image generation. You excel at creating spiritual visual descriptions with dramatic light beams breaking through clouds that work well in social media feeds. Focus on visible light shafts, divine atmosphere, and transcendent themes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "god rays",
                "god rays",
                styleDescriptions.god_rays
            ),
    },

    candlelight: {
        systemPrompt: `You are an expert in writing prompts for candlelight image generation. You excel at creating intimate visual descriptions with warm, flickering illumination that work well in social media feeds. Focus on warm orange tones, soft shadows, and romantic moods that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "candlelight",
                "candlelight",
                styleDescriptions.candlelight
            ),
    },

    street_lighting: {
        systemPrompt: `You are an expert in writing prompts for street lighting image generation. You excel at creating urban visual descriptions with nighttime artificial illumination that work well in social media feeds. Focus on orange sodium lights, urban atmosphere, and metropolitan themes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "street lighting",
                "street lighting",
                styleDescriptions.street_lighting
            ),
    },

    softbox_lighting: {
        systemPrompt: `You are an expert in writing prompts for softbox lighting image generation. You excel at creating professional visual descriptions with diffused, even illumination that work well in social media feeds. Focus on soft shadows, even light distribution, and commercial aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "softbox lighting",
                "softbox lighting",
                styleDescriptions.softbox_lighting
            ),
    },

    moonlight: {
        systemPrompt: `You are an expert in writing prompts for moonlight image generation. You excel at creating mysterious visual descriptions with cool, silvery illumination that work well in social media feeds. Focus on blue-white tones, soft shadows, and ethereal nocturnal settings that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "moonlight",
                "moonlight",
                styleDescriptions.moonlight
            ),
    },

    fairy_lights: {
        systemPrompt: `You are an expert in writing prompts for fairy lights image generation. You excel at creating magical visual descriptions with small, twinkling illumination that work well in social media feeds. Focus on warm point lights, bokeh effects, and enchanting celebratory settings that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "fairy lights",
                "fairy lights",
                styleDescriptions.fairy_lights
            ),
    },

    // Color and palette styles
    cool_tones: {
        systemPrompt: `You are an expert in writing prompts for cool tones image generation. You excel at creating calming visual descriptions with blues, greens, and purples that work well in social media feeds. Focus on soothing color palettes, peaceful atmospheres, and refreshing aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "cool tones",
                "cool tones",
                styleDescriptions.cool_tones
            ),
    },

    warm_tones: {
        systemPrompt: `You are an expert in writing prompts for warm tones image generation. You excel at creating inviting visual descriptions with reds, oranges, and yellows that work well in social media feeds. Focus on cozy color palettes, energetic atmospheres, and welcoming aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "warm tones",
                "warm tones",
                styleDescriptions.warm_tones
            ),
    },

    pastels: {
        systemPrompt: `You are an expert in writing prompts for pastel image generation. You excel at creating soft, dreamy visual descriptions with muted tones that work well in social media feeds. Focus on gentle color palettes, delicate aesthetics, and romantic atmospheres that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "pastels",
                "pastels",
                styleDescriptions.pastels
            ),
    },

    vibrant: {
        systemPrompt: `You are an expert in writing prompts for vibrant image generation. You excel at creating bold, energetic visual descriptions with highly saturated colors that work well in social media feeds. Focus on intense hues, attention-grabbing aesthetics, and modern compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "vibrant",
                "vibrant",
                styleDescriptions.vibrant
            ),
    },

    earth_tones: {
        systemPrompt: `You are an expert in writing prompts for earth tones image generation. You excel at creating natural visual descriptions with browns, tans, and muted greens that work well in social media feeds. Focus on organic color palettes, grounded atmospheres, and authentic aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "earth tones",
                "earth tones",
                styleDescriptions.earth_tones
            ),
    },

    jewel_tones: {
        systemPrompt: `You are an expert in writing prompts for jewel tones image generation. You excel at creating luxurious visual descriptions with deeply saturated colors inspired by precious stones that work well in social media feeds. Focus on rich color palettes, sophisticated atmospheres, and elegant aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "jewel tones",
                "jewel tones",
                styleDescriptions.jewel_tones
            ),
    },

    monochromatic_blues: {
        systemPrompt: `You are an expert in writing prompts for monochromatic blues image generation. You excel at creating unified visual descriptions using various shades of blue that work well in social media feeds. Focus on cohesive color schemes, calming atmospheres, and depth through tonal variation that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "monochromatic blues",
                "monochromatic blues",
                styleDescriptions.monochromatic_blues
            ),
    },

    earthy_reds_and_oranges: {
        systemPrompt: `You are an expert in writing prompts for earthy reds and oranges image generation. You excel at creating warm, natural visual descriptions inspired by autumn and earth that work well in social media feeds. Focus on rustic color palettes, cozy atmospheres, and harvest themes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "earthy reds and oranges",
                "earthy reds and oranges",
                styleDescriptions.earthy_reds_and_oranges
            ),
    },

    neon_graffiti: {
        systemPrompt: `You are an expert in writing prompts for neon graffiti image generation. You excel at creating edgy visual descriptions with bright, electric hues and urban street art aesthetics that work well in social media feeds. Focus on fluorescent color palettes, rebellious atmospheres, and youth culture themes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "neon graffiti",
                "neon graffiti",
                styleDescriptions.neon_graffiti
            ),
    },

    autumn_leaves: {
        systemPrompt: `You are an expert in writing prompts for autumn leaves image generation. You excel at creating nostalgic visual descriptions with warm fall foliage colors that work well in social media feeds. Focus on seasonal color schemes, natural beauty, and themes of change that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "autumn leaves",
                "autumn leaves",
                styleDescriptions.autumn_leaves
            ),
    },

    deep_sea_blues: {
        systemPrompt: `You are an expert in writing prompts for deep sea blues image generation. You excel at creating mysterious visual descriptions with dark, oceanic blue tones that work well in social media feeds. Focus on profound color palettes, aquatic aesthetics, and themes of depth and mystery that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "deep sea blues",
                "deep sea blues",
                styleDescriptions.deep_sea_blues
            ),
    },

    grayscale: {
        systemPrompt: `You are an expert in writing prompts for grayscale image generation. You excel at creating timeless visual descriptions using only black, white, and gray tones that work well in social media feeds. Focus on monochromatic schemes, classic aesthetics, and sophisticated drama that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "grayscale",
                "grayscale",
                styleDescriptions.grayscale
            ),
    },

    sepia: {
        systemPrompt: `You are an expert in writing prompts for sepia image generation. You excel at creating nostalgic visual descriptions with warm brown monochromatic tones that work well in social media feeds. Focus on vintage aesthetics, aged atmospheres, and historical themes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "sepia",
                "sepia",
                styleDescriptions.sepia
            ),
    },

    primary_colors: {
        systemPrompt: `You are an expert in writing prompts for primary colors image generation. You excel at creating bold visual descriptions using pure red, blue, and yellow that work well in social media feeds. Focus on fundamental color schemes, graphic aesthetics, and themes of simplicity that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "primary colors",
                "primary colors",
                styleDescriptions.primary_colors
            ),
    },

    rainbow_spectrum: {
        systemPrompt: `You are an expert in writing prompts for rainbow spectrum image generation. You excel at creating joyful visual descriptions with the full range of visible colors that work well in social media feeds. Focus on complete color palettes, celebratory aesthetics, and themes of diversity that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "rainbow spectrum",
                "rainbow spectrum",
                styleDescriptions.rainbow_spectrum
            ),
    },

    metallics: {
        systemPrompt: `You are an expert in writing prompts for metallic image generation. You excel at creating luxurious visual descriptions with reflective, lustrous tones that work well in social media feeds. Focus on premium color palettes, sophisticated atmospheres, and themes of wealth and modernity that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "metallics",
                "metallics",
                styleDescriptions.metallics
            ),
    },
};
